package qidian.it.mybatisplus.util;

import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.List;
import java.util.Random;

public class FileUtil {
    public static String upload(MultipartFile file){
        // 获取文件名
        String fileName = file.getOriginalFilename();
        System.out.println("=== 文件上传开始 ===");
        System.out.println("原始文件名: " + fileName);

        // 获取项目根路径，然后拼接 src/main/resources/uploads 路径
        String projectPath = System.getProperty("user.dir");
        String uploadDir = Paths.get(projectPath, "src", "main", "resources", "uploads").toString();
        System.out.println("项目根路径: " + projectPath);
        System.out.println("上传目录: " + uploadDir);

        // 确保目录存在
        File uploadDirFile = new File(uploadDir);
        if (!uploadDirFile.exists()) {
            boolean created = uploadDirFile.mkdirs();
            System.out.println("创建上传目录: " + created);
        } else {
            System.out.println("上传目录已存在");
        }

        // 生成新的文件名，避免重复
        String newFileName = (new Random().nextInt(1000) + 100) + fileName;
        System.out.println("新文件名: " + newFileName);

        // 保存文件
        File dest = new File(uploadDir, newFileName);
        System.out.println("目标文件路径: " + dest.getAbsolutePath());

        try {
            file.transferTo(dest);
            String fileUrl = "http://localhost:8083/uploads/" + newFileName;
            System.out.println("文件上传成功!");
            System.out.println("访问URL: " + fileUrl);
            System.out.println("=== 文件上传完成 ===");
            return fileUrl;
        } catch (IOException e) {
            System.err.println("文件上传失败: " + e.getMessage());
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }





}
