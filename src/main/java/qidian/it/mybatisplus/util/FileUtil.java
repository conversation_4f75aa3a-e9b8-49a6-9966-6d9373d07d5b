package qidian.it.mybatisplus.util;

import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.List;
import java.util.Random;

public class FileUtil {
    public static String upload(MultipartFile file){
        // 获取文件名
        String fileName = file.getOriginalFilename();

        // 获取项目根路径，然后拼接 src/main/resources/uploads 路径
        String projectPath = System.getProperty("user.dir");
        String uploadDir = Paths.get(projectPath, "src", "main", "resources", "uploads").toString();

        // 确保目录存在
        File uploadDirFile = new File(uploadDir);
        if (!uploadDirFile.exists()) {
            uploadDirFile.mkdirs();
        }

        // 生成新的文件名，避免重复
        String newFileName = (new Random().nextInt(1000) + 100) + fileName;

        // 保存文件
        File dest = new File(uploadDir, newFileName);

        try {
            file.transferTo(dest);
            String fileUrl = "http://localhost:8083/uploads/" + newFileName;
            return fileUrl;
        } catch (IOException e) {
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }





}
