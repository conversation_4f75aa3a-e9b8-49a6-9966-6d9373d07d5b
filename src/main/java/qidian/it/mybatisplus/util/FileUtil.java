package qidian.it.mybatisplus.util;

import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.List;
import java.util.Random;

public class FileUtil {
    public static String upload(MultipartFile file){
        // 获取文件名
        String fileName = file.getOriginalFilename();
        // 确定文件存储的路径，假设是与 src 同级的 upload 文件夹
        // 使用相对路径来获取上传目录
        String uploadDir = Paths.get("uploads").toAbsolutePath().normalize().toString();
        System.out.println(uploadDir);
        // 确保目录存在
        File uploadDirFile = new File(uploadDir);
        if (!uploadDirFile.exists()) {
            uploadDirFile.mkdirs(); // 如果文件夹不存在则创建
        }


        String newFileName=(new Random().nextInt(1000)+100)+ fileName;
        // 保存文件
        File dest = new File(uploadDir,newFileName);
        try {
            file.transferTo(dest);
            return "http://localhost:8083/uploads/"+newFileName;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }





}
