package qidian.it.mybatisplus.serviceimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import qidian.it.mybatisplus.config.JwtConfig;
import qidian.it.mybatisplus.entity.Admin;
import qidian.it.mybatisplus.entity.Result;
import qidian.it.mybatisplus.mapper.AdminMapper;
import qidian.it.mybatisplus.service.IAdminService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Service
public class AdminServiceImpl extends ServiceImpl<AdminMapper, Admin> implements IAdminService {

    @Autowired
    private JwtConfig jwtConfig;
    /**
     * 管理员登录
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    public Result login(String username, String password) {
        // 参数校验
        if (username == null || username.trim().isEmpty()) {
            return Result.fail("用户名不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            return Result.fail("密码不能为空");
        }

        // 查询管理员
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Admin::getUsername, username.trim());
        Admin admin = baseMapper.selectOne(queryWrapper);

        if (Objects.nonNull(admin)) {
            // 简单的密码验证（实际项目中应该使用加密密码）
            if (password.equals(admin.getPassword())) {
                // 登录成功，生成token
                String token = jwtConfig.createToken(username);

                // 返回管理员信息（不包含密码）和token
                admin.setPassword(null);

                // 创建包含token的返回数据
                java.util.Map<String, Object> data = new java.util.HashMap<>();
                data.put("admin", admin);
                data.put("token", token);

                return Result.success("登录成功", data);
            } else {
                return Result.fail("用户名或密码错误");
            }
        }
        return Result.fail("用户名或密码错误");
    }

    @Override
    public Result register(Admin admin) {
        LambdaQueryWrapper<Admin> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        Admin adminSelect=baseMapper.selectOne(lambdaQueryWrapper.eq(Admin::getUsername,admin.getUsername()));
        if(Objects.isNull(adminSelect)){//允许注册
            if(baseMapper.insert(admin)>0){
                return Result.success("注册成功");
            }else{
                return Result.fail("服务繁忙,请稍候");
            }
        }
        return Result.fail("用户名已存在");
    }

    /**
     * 检查登录状态
     * @param username 用户名
     * @return 登录状态结果
     */
    public Result checkLogin(String username) {
        if (username == null || username.trim().isEmpty()) {
            return Result.success(false);
        }

        // 查询用户是否存在
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Admin::getUsername, username.trim());
        Admin admin = baseMapper.selectOne(queryWrapper);

        // 简单的登录状态检查：如果用户存在就认为已登录
        // 实际项目中应该检查session或token
        return Result.success(Objects.nonNull(admin) ? admin : false);
    }

}
