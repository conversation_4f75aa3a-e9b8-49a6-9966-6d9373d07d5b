package qidian.it.mybatisplus.aspect;


import io.jsonwebtoken.Claims;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import qidian.it.mybatisplus.config.JwtConfig;
import qidian.it.mybatisplus.entity.Result;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @company 起点编程
 * @Date: 2025/4/11 10:31
 * @Description
 */

@Aspect
@Component
public class TokenAspect {
    @Autowired
    JwtConfig jwtConfig;

    @Pointcut("@annotation(qidian.it.mybatisplus.annotation.RequiredToken)")
    public void checkToken(){}

    //1.请求进入后端==>方法上加了@RequiredToken注解:
    // 需要验证token,验证通过后执行目标方法,验证不通过:不执行目标方法

    @Around(value="checkToken()")
    public Result aroundMethod(ProceedingJoinPoint point){
//AOP中获取HttpServletRequest
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        System.out.println(request);
        Result result=new Result();
        String methodName = point.getSignature().getName();
        List list= Arrays.asList(point.getArgs());
        try {
            //前置通知
            System.out.println("我是前置通知,即将调用"+ methodName+"参数是"+list);
            /** Token 验证 */
            String token = request.getHeader(jwtConfig.getHeader());
            System.out.println("前端传过来的token===>"+token);

            if(ObjectUtils.isEmpty(token)){
              return Result.fail("token不能为空");
            }


            Claims claims = null;
            try{
                claims = jwtConfig.getTokenClaim(token);
                if(claims==null||jwtConfig.isTokenExpired(token)){
                    return Result.fail("token已失效");
                }
            }catch (Exception e){
              return Result.fail("token已失效");
            }finally {
            }
            //执行目标方法
            result = (Result) point.proceed();
            //后置通知
            System.out.println("我是后置通知");
        } catch (Throwable e) {
            //异常通知
            System.out.println("我是异常通知,异常信息是"+e);
            throw new RuntimeException(e);
        }
        //返回通知
        System.out.println("我是返回通知,方法返回值是"+result);
        return result;
    }


}
