package qidian.it.mybatisplus.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import qidian.it.mybatisplus.interceptor.MyInterceptor;

@Configuration
public class InterceptorConfig implements WebMvcConfigurer {
    @Autowired
    MyInterceptor myInterceptor;

    //注册拦截器
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //registry.addInterceptor(myInterceptor).addPathPatterns("/**").excludePathPatterns("/getAllEmp");//拦截所有请求
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 将上传文件的路径暴露为静态资源
        // 获取项目根路径，与FileUtil保持一致
        String projectPath = System.getProperty("user.dir");
        String uploadPath = "file:" + projectPath + "/src/main/resources/uploads/";

        System.out.println("=== 静态资源配置信息 ===");
        System.out.println("项目根路径: " + projectPath);
        System.out.println("上传文件路径: " + uploadPath);
        System.out.println("URL映射: /uploads/** -> " + uploadPath);

        registry.addResourceHandler("/uploads/**")  // URL前缀
                .addResourceLocations(uploadPath);  // 上传文件的实际路径

        // 同时添加classpath路径作为备选
        registry.addResourceHandler("/static/uploads/**")
                .addResourceLocations("classpath:/uploads/");

        System.out.println("静态资源配置完成");
    }


}
