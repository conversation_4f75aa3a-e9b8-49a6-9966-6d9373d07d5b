package qidian.it.mybatisplus.interceptor;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import qidian.it.mybatisplus.config.JwtConfig;
import qidian.it.mybatisplus.entity.Result;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Objects;

@Component
public class MyInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtConfig jwtConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        System.out.println("JWT拦截器 - preHandle");

        // 设置响应编码和内容类型
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json;charset=UTF-8");

        // 从请求头中获取token
        String token = request.getHeader("Authorization");

        // 检查token是否存在
        if (token == null || token.trim().isEmpty()) {
            System.out.println("Token不存在");
            writeErrorResponse(response, "未提供认证token");
            return false;
        }

        // 如果token以"Bearer "开头，去掉前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }

        try {
            // 验证token有效性
            if (!jwtConfig.validateToken(token)) {
                System.out.println("Token无效");
                writeErrorResponse(response, "Token无效");
                return false;
            }

            // 检查token是否过期
            if (jwtConfig.isTokenExpired(token)) {
                System.out.println("Token已过期");
                writeErrorResponse(response, "Token已过期，请重新登录");
                return false;
            }

            // 从token中获取用户名并设置到请求属性中，供后续使用
            String username = jwtConfig.getUsernameFromToken(token);
            request.setAttribute("username", username);

            System.out.println("Token验证成功，用户: " + username);
            return true;

        } catch (Exception e) {
            System.out.println("Token验证异常: " + e.getMessage());
            writeErrorResponse(response, "Token验证失败");
            return false;
        }
    }

    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, String message) throws Exception {
        PrintWriter printWriter = response.getWriter();
        Result errorResult = Result.fail(message);
        printWriter.println(JSON.toJSONString(errorResult));
        printWriter.flush();
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        System.out.println("postHandle");

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        System.out.println("afterCompletion");
    }



}
