package qidian.it.mybatisplus;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@MapperScan("qidian.it.mybatisplus.mapper")
public class MybatisplusApplication {

    public static void main(String[] args) {
        System.out.println("=== 启动Spring Boot应用 ===");
        SpringApplication.run(MybatisplusApplication.class, args);
        System.out.println("=== 应用启动完成 ===");
    }

}
