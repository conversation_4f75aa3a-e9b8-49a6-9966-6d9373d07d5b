package qidian.it.mybatisplus.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import qidian.it.mybatisplus.config.JwtConfig;
import qidian.it.mybatisplus.entity.Admin;
import qidian.it.mybatisplus.entity.Result;
import qidian.it.mybatisplus.serviceimpl.AdminServiceImpl;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@RestController
@CrossOrigin
@RequestMapping("/admin")
public class AdminController {

    @Autowired
    AdminServiceImpl adminService;
    @Autowired
    JwtConfig jwtConfig;

    /**
     * 管理员登录
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    @RequestMapping("/login")
    public Result login(@RequestParam String username, @RequestParam String password) {
        String token = jwtConfig.createToken(username);
        return adminService.login(username, password);
    }

    @RequestMapping("/register")
    public Result register(Admin admin){
        return adminService.register(admin);
    }

    /**
     * 检查登录状态
     * @param username 用户名
     * @return 登录状态
     */
    @RequestMapping("/checkLogin")
    public Result checkLogin(@RequestParam String username) {
        return adminService.checkLogin(username);
    }

    @RequestMapping(value = "/invalid",produces = "application/json;charset=utf-8")
    public Result invalid(String username){
        return Result.success("访问成功");
    }


}


