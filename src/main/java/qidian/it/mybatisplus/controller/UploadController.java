package qidian.it.mybatisplus.controller;

import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import qidian.it.mybatisplus.util.FileUtil;

@RestController
@CrossOrigin
public class UploadController {
    @RequestMapping("/upload")
    public String upload(MultipartFile file){
        if (file == null || file.isEmpty()) {
            return "文件不能为空";
        }

        try {
            String fileUrl = FileUtil.upload(file);
            System.out.println("文件上传成功，访问URL: " + fileUrl);
            return fileUrl;
        } catch (Exception e) {
            e.printStackTrace();
            return "文件上传失败：" + e.getMessage();
        }
    }
}
