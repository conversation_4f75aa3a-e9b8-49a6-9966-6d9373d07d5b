package qidian.it.mybatisplus.controller;

import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import qidian.it.mybatisplus.util.FileUtil;

@RestController
@CrossOrigin
public class UploadController {
    @RequestMapping("/upload")
    public String upload(MultipartFile file){
        System.out.println("=== 收到文件上传请求 ===");

        if (file == null || file.isEmpty()) {
            System.out.println("文件为空，上传失败");
            return "文件不能为空";
        }

        System.out.println("文件信息:");
        System.out.println("- 文件名: " + file.getOriginalFilename());
        System.out.println("- 文件大小: " + file.getSize() + " bytes");
        System.out.println("- 文件类型: " + file.getContentType());

        try {
            String fileUrl = FileUtil.upload(file);
            System.out.println("控制器返回URL: " + fileUrl);
            return fileUrl;
        } catch (Exception e) {
            System.err.println("控制器捕获异常: " + e.getMessage());
            e.printStackTrace();
            return "文件上传失败：" + e.getMessage();
        }
    }
}
