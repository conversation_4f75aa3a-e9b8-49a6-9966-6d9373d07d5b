package qidian.it.mybatisplus.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import qidian.it.mybatisplus.entity.Employee;
import qidian.it.mybatisplus.entity.Result;
import qidian.it.mybatisplus.serviceimpl.EmployeeServiceImpl;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@RestController
@CrossOrigin
@RequestMapping("/employee")
public class EmployeeController {

    @Autowired
    EmployeeServiceImpl employeeService;

    @RequestMapping("/getAllEmp")
    public Result getAllEmp(Integer currentPage){
        System.out.println("进入controller");
        return employeeService.getAllEmp(currentPage);
    }

@RequestMapping("/updateEmp")
    public Result updateEmp(Employee employee) {
    return employeeService.updateEmp(employee);
    }

    @RequestMapping("/exportData")
    public void exportData(HttpServletResponse resp) {
        employeeService.exportData(resp);
    }


    }
